import request from "@/service";

// 获取设备预约区域列表
export function getDeviceReservationAreaList(params) {
  return request.get("/eccard-ops/deviceReservation/areaList", { params });
}

// 新增设备预约区域
export function addDeviceReservationArea(params) {
  return request.post("/eccard-ops/deviceReservation/area", params);
}

// 修改设备预约区域
export function updateDeviceReservationArea(params) {
  return request.put("/eccard-ops/deviceReservation/area", params);
}

// 删除设备预约区域
export function deleteDeviceReservationArea(id) {
  const tenantId = JSON.parse(sessionStorage.getItem("kade_cache_userinfo"))
    .tenantId;
  return request.delete(`/eccard-ops/deviceReservation/area/${tenantId}/${id}`);
}

// 获取设备预约区域详情
export function getDeviceReservationAreaDetail(id) {
  return request.get(`/eccard-ops/deviceReservation/area/${id}`);
}

// 获取区域预约时间段列表
export function getAreaReservationTimeList(params) {
  return request.get("/eccard-ops/deviceReservation/timeSlot/list", { params });
}

// 新增区域预约时间段
export function addAreaReservationTime(params) {
  return request.post("/eccard-ops/deviceReservation/timeSlot", params);
}

// 修改区域预约时间段
export function updateAreaReservationTime(params) {
  return request.put("/eccard-ops/deviceReservation/timeSlot", params);
}

// 删除区域预约时间段
export function deleteAreaReservationTime(id) {
  const tenantId = JSON.parse(sessionStorage.getItem("kade_cache_userinfo"))
    .tenantId;
  return request.delete(`/eccard-ops/deviceReservation/timeSlot/${tenantId}/${id}`);
}
