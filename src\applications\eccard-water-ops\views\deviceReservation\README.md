# 设备预约管理功能

## 功能概述

设备预约管理功能用于管理设备预约的区域信息，包括区域的基本信息和预约时间段的配置。

## 功能特性

### 1. 区域信息管理
- **进入区域显示预约的设备信息**：支持按区域筛选和查看设备预约信息
- **区域名称**：显示设备预约区域的名称
- **区域标签**：显示区域的标识标签
- **上级区域**：显示区域的层级关系
- **区域地址**：显示区域的具体地址位置

### 2. 预约时间段管理
- **预约时间段**：显示该区域可预约的时间段
- **时间段配置**：支持为每个区域配置多个预约时间段
- **时间段验证**：确保结束时间大于开始时间
- **时间段删除**：支持删除不需要的时间段

### 3. 启用状态管理
- **是否启用**：控制区域是否开放预约
- **状态筛选**：支持按启用状态筛选区域
- **状态标识**：用不同颜色的标签显示启用状态

### 4. 操作功能
- **新增区域**：添加新的设备预约区域
- **编辑区域**：修改区域的基本信息
- **删除区域**：删除不需要的区域（需确认）
- **预约时间管理**：为区域配置预约时间段

## 页面结构

### 搜索筛选区域
- 所属区域选择器
- 区域名称输入框
- 启用状态下拉选择

### 数据列表区域
- 区域信息表格显示
- 分页控件
- 操作按钮（编辑、预约时间、删除）

### 弹窗组件
1. **区域编辑弹窗**
   - 区域名称输入
   - 区域标签输入
   - 上级区域选择
   - 区域地址输入
   - 启用状态选择

2. **预约时间段管理弹窗**
   - 时间段列表显示
   - 时间段新增/编辑表单
   - 开始时间和结束时间选择器

## 技术实现

### 前端技术栈
- Vue 3 Composition API
- Element Plus UI组件库
- 响应式数据管理

### 组件依赖
- `kade-area-select-tree`：区域选择树组件
- `kade-table-filter`：表格筛选组件
- `kade-table-wrap`：表格包装组件
- `kade-route-card`：路由卡片组件

### API接口（待实现）
- `getDeviceReservationAreaList`：获取区域列表
- `addDeviceReservationArea`：新增区域
- `updateDeviceReservationArea`：更新区域
- `deleteDeviceReservationArea`：删除区域
- `getAreaReservationTimeList`：获取时间段列表
- `addAreaReservationTime`：新增时间段
- `updateAreaReservationTime`：更新时间段
- `deleteAreaReservationTime`：删除时间段

## 数据结构

### 区域信息
```javascript
{
  id: number,                    // 区域ID
  areaName: string,             // 区域名称
  areaLabel: string,            // 区域标签
  parentAreaName: string,       // 上级区域名称
  parentAreaId: number,         // 上级区域ID
  areaAddress: string,          // 区域地址
  status: string,               // 启用状态 ('1'启用, '0'禁用)
  reservationTimeSlots: Array   // 预约时间段列表
}
```

### 时间段信息
```javascript
{
  id: number,           // 时间段ID
  startTime: string,    // 开始时间 (HH:mm格式)
  endTime: string,      // 结束时间 (HH:mm格式)
  duration: number,     // 时长(分钟)
  areaId: number        // 所属区域ID
}
```

## 使用说明

1. **查看区域列表**：页面加载后自动显示所有设备预约区域
2. **筛选区域**：使用顶部筛选条件缩小查询范围
3. **新增区域**：点击"新增"按钮，填写区域信息
4. **编辑区域**：点击表格中的"编辑"按钮修改区域信息
5. **配置时间段**：点击"预约时间"按钮为区域配置可预约的时间段
6. **删除操作**：点击"删除"按钮可删除区域或时间段（需确认）

## 注意事项

1. 当前版本使用模拟数据，实际部署时需要连接真实的后端API
2. 时间段配置时需确保时间的合理性（结束时间 > 开始时间）
3. 删除操作会弹出确认对话框，避免误删
4. 区域的启用状态会影响该区域是否可以进行设备预约
