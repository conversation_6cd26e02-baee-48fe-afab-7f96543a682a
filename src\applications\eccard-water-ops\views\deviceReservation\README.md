# 设备预约管理功能

## 功能概述

设备预约管理功能用于管理设备预约的区域信息，包括区域的基本信息和预约时间段的配置。

## 🆕 新增功能特性

### 1. 增强的新增/编辑功能
- **所属区域选择**：在新增时可以直接选择所属区域
- **预约时间段配置**：在新增/编辑弹窗中直接配置多个时间段
- **是否启用设置**：直接在表单中设置区域的启用状态
- **实时时长计算**：自动计算每个时间段的持续时间

### 2. 新增弹窗包含的字段
1. **区域名称** - 必填，区域的显示名称
2. **区域标签** - 必填，区域的唯一标识
3. **所属区域** - 可选，选择上级区域建立层级关系
4. **区域地址** - 必填，区域的具体位置
5. **是否启用** - 必选，控制区域是否开放预约
6. **预约时间段** - 可选，配置多个可预约的时间段

### 3. 时间段配置功能
- **添加时间段**：点击"添加时间段"按钮新增时间段
- **时间选择器**：使用时间选择器设置开始和结束时间
- **自动计算时长**：实时显示每个时间段的持续时间（分钟）
- **删除时间段**：可以删除不需要的时间段
- **时间验证**：确保结束时间大于开始时间

## 使用说明

### 新增区域操作步骤

1. **点击新增按钮**
   - 在页面顶部点击"新增"按钮
   - 弹出新增区域对话框

2. **填写基本信息**
   - 输入区域名称（必填）
   - 输入区域标签（必填）
   - 选择所属区域（可选）
   - 输入区域地址（必填）
   - 选择是否启用状态

3. **配置预约时间段**
   - 点击"添加时间段"按钮
   - 选择开始时间和结束时间
   - 系统自动计算时长
   - 可添加多个时间段
   - 可删除不需要的时间段

4. **提交保存**
   - 点击"确定"按钮保存
   - 系统会验证时间段的合理性
   - 保存成功后刷新列表

### 编辑区域操作步骤

1. **点击编辑按钮**
   - 在列表中点击某行的"编辑"按钮
   - 弹出编辑区域对话框

2. **修改信息**
   - 修改需要更新的字段
   - 时间段会自动加载现有配置
   - 可以添加、修改或删除时间段

3. **保存更改**
   - 点击"确定"按钮保存更改
   - 系统验证后更新数据

## 数据验证规则

### 基本信息验证
- 区域名称：不能为空
- 区域标签：不能为空
- 区域地址：不能为空
- 是否启用：必须选择

### 时间段验证
- 开始时间和结束时间不能为空
- 结束时间必须大于开始时间
- 时间格式为 HH:mm（24小时制）

## 界面布局

### 新增/编辑弹窗布局
```
┌─────────────────────────────────────────────────────────────┐
│                    新增/编辑区域                              │
├─────────────────────────────────────────────────────────────┤
│  区域名称: [输入框]        区域标签: [输入框]                │
│  所属区域: [区域选择器]    是否启用: ○启用 ○禁用             │
│  区域地址: [输入框]                                         │
│                                                             │
│  预约时间段:                                                │
│  [添加时间段]                                               │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ 开始时间 │ 结束时间 │ 时长(分钟) │ 操作              │   │
│  ├─────────────────────────────────────────────────────┤   │
│  │ [时间选择] │ [时间选择] │   120    │ [删除]           │   │
│  └─────────────────────────────────────────────────────┘   │
│                                                             │
│                                    [取消] [确定]            │
└─────────────────────────────────────────────────────────────┘
```

## 技术实现要点

### 响应式布局
- 使用 el-row 和 el-col 实现响应式布局
- 弹窗宽度调整为 800px 以容纳更多内容

### 数据结构
```javascript
formData: {
  areaName: '',           // 区域名称
  areaLabel: '',          // 区域标签
  parentAreaId: null,     // 所属区域ID
  areaAddress: '',        // 区域地址
  status: '1',            // 是否启用
  timeSlots: [            // 时间段数组
    {
      startTime: '08:00',
      endTime: '12:00'
    }
  ]
}
```

### 时长计算算法
```javascript
const calculateDuration = (startTime, endTime) => {
  if (!startTime || !endTime) return 0;
  
  const start = new Date(`2000-01-01 ${startTime}`);
  const end = new Date(`2000-01-01 ${endTime}`);
  
  if (end <= start) return 0;
  
  return Math.round((end - start) / (1000 * 60));
};
```

## 注意事项

1. **时间段配置**：建议合理安排时间段，避免重叠
2. **数据验证**：系统会自动验证时间段的合理性
3. **保存机制**：时间段数据会转换为 reservationTimeSlots 字段保存
4. **编辑加载**：编辑时会自动加载现有的时间段配置
