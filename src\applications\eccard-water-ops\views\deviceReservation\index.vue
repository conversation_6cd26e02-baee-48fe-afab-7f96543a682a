<template>
  <div class="deviceReservation">
    <kade-route-card style="height: auto">
      <kade-table-filter @search="handleSearch" @reset="handleReset">
        <el-form inline size="small" label-width="100px">
          <el-form-item label="所属区域">
            <kade-area-select-tree style="width: 200px" :value="state.form.areaId" valueKey="id" :multiple="false"
              @valueChange="(val) => (state.form.areaId = val?.id)" />
          </el-form-item>
          <el-form-item label="区域名称">
            <el-input v-model="state.form.areaName" placeholder="请输入区域名称" style="width: 200px" clearable />
          </el-form-item>
          <el-form-item label="是否启用">
            <el-select v-model="state.form.status" placeholder="请选择" style="width: 200px" clearable>
              <el-option label="启用" value="1"></el-option>
              <el-option label="禁用" value="0"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </kade-table-filter>
      <kade-table-wrap title="设备预约区域信息">
        <template #header>
          <el-button type="primary" size="small" @click="handleAdd">新增</el-button>
        </template>
        <el-table style="width: 100%" :data="state.dataList" ref="multipleTable" v-loading="state.loading" height="55vh"
          highlight-current-row border stripe>
          <el-table-column show-overflow-tooltip label="区域名称" prop="areaName" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="区域标签" prop="areaLabel" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="上级区域" prop="parentAreaName" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="区域地址" prop="areaAddress" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="预约时间段" prop="reservationTime" align="center">
            <template #default="scope">
              <span v-if="scope.row.reservationTimeSlots && scope.row.reservationTimeSlots.length > 0">
                {{ scope.row.reservationTimeSlots.map(slot => `${slot.startTime}-${slot.endTime}`).join(', ') }}
              </span>
              <span v-else>未设置</span>
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip label="是否启用" prop="status" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.status === '1' ? 'success' : 'danger'">
                {{ scope.row.status === '1' ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="200">
            <template #default="scope">
              <el-button size="mini" type="text" @click="handleEdit(scope.row)">编辑</el-button>
              <el-button size="mini" type="text" @click="handleTimeSlot(scope.row)">预约时间</el-button>
              <el-button size="mini" type="text" @click="handleDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination background :current-page="state.form.pageNum" :page-size="state.form.pageSize"
            layout="total, sizes, prev, pager, next, jumper" :page-sizes="[6, 10, 20, 50, 100]" :total="state.total"
            @current-change="handlePageChange" @size-change="handleSizeChange">
          </el-pagination>
        </div>
      </kade-table-wrap>
    </kade-route-card>

    <!-- 新增/编辑区域弹窗 -->
    <el-dialog v-model="state.dialogVisible" :title="state.dialogTitle" width="600px" @close="handleDialogClose">
      <el-form :model="state.formData" :rules="state.rules" ref="formRef" label-width="120px">
        <el-form-item label="区域名称" prop="areaName">
          <el-input v-model="state.formData.areaName" placeholder="请输入区域名称" />
        </el-form-item>
        <el-form-item label="区域标签" prop="areaLabel">
          <el-input v-model="state.formData.areaLabel" placeholder="请输入区域标签" />
        </el-form-item>
        <el-form-item label="上级区域" prop="parentAreaId">
          <kade-area-select-tree style="width: 100%" :value="state.formData.parentAreaId" valueKey="id" :multiple="false"
            @valueChange="(val) => (state.formData.parentAreaId = val?.id)" />
        </el-form-item>
        <el-form-item label="区域地址" prop="areaAddress">
          <el-input v-model="state.formData.areaAddress" placeholder="请输入区域地址" />
        </el-form-item>
        <el-form-item label="是否启用" prop="status">
          <el-radio-group v-model="state.formData.status">
            <el-radio label="1">启用</el-radio>
            <el-radio label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="state.dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="state.submitLoading">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 预约时间段管理弹窗 -->
    <el-dialog v-model="state.timeSlotDialogVisible" title="预约时间段管理" width="800px" @close="handleTimeSlotDialogClose">
      <div style="margin-bottom: 20px;">
        <el-button type="primary" size="small" @click="handleAddTimeSlot">新增时间段</el-button>
      </div>
      <el-table :data="state.timeSlotList" border>
        <el-table-column label="开始时间" prop="startTime" align="center"></el-table-column>
        <el-table-column label="结束时间" prop="endTime" align="center"></el-table-column>
        <el-table-column label="预约时长(分钟)" prop="duration" align="center"></el-table-column>
        <el-table-column label="操作" align="center" width="150">
          <template #default="scope">
            <el-button size="mini" type="text" @click="handleEditTimeSlot(scope.row)">编辑</el-button>
            <el-button size="mini" type="text" @click="handleDeleteTimeSlot(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 时间段编辑表单 -->
      <el-form v-if="state.timeSlotFormVisible" :model="state.timeSlotFormData" :rules="state.timeSlotRules"
        ref="timeSlotFormRef" label-width="120px" style="margin-top: 20px; padding: 20px; border: 1px solid #eee;">
        <el-form-item label="开始时间" prop="startTime">
          <el-time-picker v-model="state.timeSlotFormData.startTime" format="HH:mm" value-format="HH:mm"
            placeholder="选择开始时间" />
        </el-form-item>
        <el-form-item label="结束时间" prop="endTime">
          <el-time-picker v-model="state.timeSlotFormData.endTime" format="HH:mm" value-format="HH:mm"
            placeholder="选择结束时间" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSubmitTimeSlot" :loading="state.timeSlotSubmitLoading">保存</el-button>
          <el-button @click="handleCancelTimeSlot">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
import {
  ElTable,
  ElTableColumn,
  ElForm,
  ElFormItem,
  ElPagination,
  ElButton,
  ElDialog,
  ElInput,
  ElSelect,
  ElOption,
  ElTag,
  ElRadioGroup,
  ElRadio,
  ElTimePicker,
  ElMessage,
  ElMessageBox,
} from "element-plus";
import { reactive, onMounted, ref } from "vue";
import {
  getDeviceReservationAreaList,
  addDeviceReservationArea,
  updateDeviceReservationArea,
  deleteDeviceReservationArea,
  getAreaReservationTimeList,
  addAreaReservationTime,
  updateAreaReservationTime,
  deleteAreaReservationTime,
} from "@/applications/eccard-water-ops/api/deviceReservation";
import areaSelectTree from "@/components/tree/areaSelectTree.vue";

export default {
  components: {
    "el-table": ElTable,
    "el-table-column": ElTableColumn,
    "el-form": ElForm,
    "el-form-item": ElFormItem,
    "el-pagination": ElPagination,
    "el-button": ElButton,
    "el-dialog": ElDialog,
    "el-input": ElInput,
    "el-select": ElSelect,
    "el-option": ElOption,
    "el-tag": ElTag,
    "el-radio-group": ElRadioGroup,
    "el-radio": ElRadio,
    "el-time-picker": ElTimePicker,
    "kade-area-select-tree": areaSelectTree,
  },
  setup() {
    const formRef = ref(null);
    const timeSlotFormRef = ref(null);

    const state = reactive({
      loading: false,
      form: {
        pageSize: 10,
        pageNum: 1,
        areaId: null,
        areaName: '',
        status: '',
      },
      dataList: [],
      total: 0,

      // 弹窗相关
      dialogVisible: false,
      dialogTitle: '新增区域',
      submitLoading: false,
      formData: {
        areaName: '',
        areaLabel: '',
        parentAreaId: null,
        areaAddress: '',
        status: '1',
      },
      rules: {
        areaName: [{ required: true, message: '请输入区域名称', trigger: 'blur' }],
        areaLabel: [{ required: true, message: '请输入区域标签', trigger: 'blur' }],
        areaAddress: [{ required: true, message: '请输入区域地址', trigger: 'blur' }],
        status: [{ required: true, message: '请选择是否启用', trigger: 'change' }],
      },

      // 时间段管理相关
      timeSlotDialogVisible: false,
      timeSlotList: [],
      timeSlotFormVisible: false,
      timeSlotSubmitLoading: false,
      currentAreaId: null,
      timeSlotFormData: {
        id: null,
        startTime: '',
        endTime: '',
        areaId: null,
      },
      timeSlotRules: {
        startTime: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
        endTime: [{ required: true, message: '请选择结束时间', trigger: 'change' }],
      },
    });

    // 获取列表数据
    const getList = async () => {
      state.loading = true;
      try {
        const params = { ...state.form };
        // 清理空值
        Object.keys(params).forEach(key => {
          if (params[key] === '' || params[key] === null || params[key] === undefined) {
            delete params[key];
          }
        });

        // 暂时使用模拟数据，实际项目中应该调用真实API
        // const { data } = await getDeviceReservationAreaList(params);

        // 模拟数据
        const mockData = {
          list: [
            {
              id: 1,
              areaName: '图书馆阅览室',
              areaLabel: 'LIB001',
              parentAreaName: '图书馆',
              areaAddress: '图书馆一楼东侧',
              status: '1',
              reservationTimeSlots: [
                { startTime: '08:00', endTime: '12:00' },
                { startTime: '14:00', endTime: '18:00' }
              ]
            },
            {
              id: 2,
              areaName: '实验室A',
              areaLabel: 'LAB001',
              parentAreaName: '实验楼',
              areaAddress: '实验楼二楼201室',
              status: '1',
              reservationTimeSlots: [
                { startTime: '09:00', endTime: '17:00' }
              ]
            },
            {
              id: 3,
              areaName: '会议室B',
              areaLabel: 'MEET002',
              parentAreaName: '行政楼',
              areaAddress: '行政楼三楼会议室',
              status: '0',
              reservationTimeSlots: []
            }
          ],
          total: 3
        };

        state.dataList = mockData.list || [];
        state.total = mockData.total || 0;
      } catch (error) {
        console.error('获取列表失败:', error);
        ElMessage.error('获取列表失败');
      } finally {
        state.loading = false;
      }
    };

    // 搜索
    const handleSearch = () => {
      state.form.pageNum = 1;
      getList();
    };

    // 重置
    const handleReset = () => {
      state.form = {
        pageSize: 10,
        pageNum: 1,
        areaId: null,
        areaName: '',
        status: '',
      };
      getList();
    };

    // 分页
    const handlePageChange = (val) => {
      state.form.pageNum = val;
      getList();
    };

    const handleSizeChange = (val) => {
      state.form.pageNum = 1;
      state.form.pageSize = val;
      getList();
    };

    // 新增
    const handleAdd = () => {
      state.dialogTitle = '新增区域';
      state.dialogVisible = true;
      state.formData = {
        areaName: '',
        areaLabel: '',
        parentAreaId: null,
        areaAddress: '',
        status: '1',
      };
    };

    // 编辑
    const handleEdit = (row) => {
      state.dialogTitle = '编辑区域';
      state.dialogVisible = true;
      state.formData = { ...row };
    };

    // 删除
    const handleDelete = async (row) => {
      try {
        await ElMessageBox.confirm('确定要删除这个区域吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        });

        // 暂时使用模拟删除，实际项目中应该调用真实API
        // await deleteDeviceReservationArea(row.id);
        console.log('删除区域:', row);
        ElMessage.success('删除成功');
        getList();
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除失败:', error);
          ElMessage.error('删除失败');
        }
      }
    };

    // 提交表单
    const handleSubmit = async () => {
      try {
        await formRef.value.validate();
        state.submitLoading = true;

        // 暂时使用模拟提交，实际项目中应该调用真实API
        if (state.formData.id) {
          // await updateDeviceReservationArea(state.formData);
          console.log('修改区域:', state.formData);
          ElMessage.success('修改成功');
        } else {
          // await addDeviceReservationArea(state.formData);
          console.log('新增区域:', state.formData);
          ElMessage.success('新增成功');
        }

        state.dialogVisible = false;
        getList();
      } catch (error) {
        console.error('提交失败:', error);
        ElMessage.error('提交失败');
      } finally {
        state.submitLoading = false;
      }
    };

    // 关闭弹窗
    const handleDialogClose = () => {
      formRef.value?.resetFields();
    };

    // 预约时间段管理
    const handleTimeSlot = async (row) => {
      state.currentAreaId = row.id;
      state.timeSlotDialogVisible = true;
      await getTimeSlotList();
    };

    // 获取时间段列表
    const getTimeSlotList = async () => {
      try {
        // 暂时使用模拟数据，实际项目中应该调用真实API
        // const { data } = await getAreaReservationTimeList({ areaId: state.currentAreaId });

        // 模拟时间段数据
        const mockTimeSlots = [
          { id: 1, startTime: '08:00', endTime: '12:00', duration: 240, areaId: state.currentAreaId },
          { id: 2, startTime: '14:00', endTime: '18:00', duration: 240, areaId: state.currentAreaId },
          { id: 3, startTime: '19:00', endTime: '21:00', duration: 120, areaId: state.currentAreaId }
        ];

        state.timeSlotList = mockTimeSlots || [];
      } catch (error) {
        console.error('获取时间段列表失败:', error);
        ElMessage.error('获取时间段列表失败');
      }
    };

    // 新增时间段
    const handleAddTimeSlot = () => {
      state.timeSlotFormVisible = true;
      state.timeSlotFormData = {
        id: null,
        startTime: '',
        endTime: '',
        areaId: state.currentAreaId,
      };
    };

    // 编辑时间段
    const handleEditTimeSlot = (row) => {
      state.timeSlotFormVisible = true;
      state.timeSlotFormData = { ...row };
    };

    // 删除时间段
    const handleDeleteTimeSlot = async (row) => {
      try {
        await ElMessageBox.confirm('确定要删除这个时间段吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        });

        // 暂时使用模拟删除，实际项目中应该调用真实API
        // await deleteAreaReservationTime(row.id);
        console.log('删除时间段:', row);
        ElMessage.success('删除成功');
        getTimeSlotList();
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除失败:', error);
          ElMessage.error('删除失败');
        }
      }
    };

    // 提交时间段
    const handleSubmitTimeSlot = async () => {
      try {
        await timeSlotFormRef.value.validate();

        // 验证时间段合理性
        if (state.timeSlotFormData.startTime >= state.timeSlotFormData.endTime) {
          ElMessage.error('结束时间必须大于开始时间');
          return;
        }

        state.timeSlotSubmitLoading = true;

        // 暂时使用模拟提交，实际项目中应该调用真实API
        if (state.timeSlotFormData.id) {
          // await updateAreaReservationTime(state.timeSlotFormData);
          console.log('修改时间段:', state.timeSlotFormData);
          ElMessage.success('修改成功');
        } else {
          // await addAreaReservationTime(state.timeSlotFormData);
          console.log('新增时间段:', state.timeSlotFormData);
          ElMessage.success('新增成功');
        }

        state.timeSlotFormVisible = false;
        getTimeSlotList();
      } catch (error) {
        console.error('提交失败:', error);
        ElMessage.error('提交失败');
      } finally {
        state.timeSlotSubmitLoading = false;
      }
    };

    // 取消时间段编辑
    const handleCancelTimeSlot = () => {
      state.timeSlotFormVisible = false;
      timeSlotFormRef.value?.resetFields();
    };

    // 关闭时间段弹窗
    const handleTimeSlotDialogClose = () => {
      state.timeSlotFormVisible = false;
      timeSlotFormRef.value?.resetFields();
    };

    onMounted(() => {
      getList();
    });

    return {
      state,
      formRef,
      timeSlotFormRef,
      handleSearch,
      handleReset,
      handlePageChange,
      handleSizeChange,
      handleAdd,
      handleEdit,
      handleDelete,
      handleSubmit,
      handleDialogClose,
      handleTimeSlot,
      handleAddTimeSlot,
      handleEditTimeSlot,
      handleDeleteTimeSlot,
      handleSubmitTimeSlot,
      handleCancelTimeSlot,
      handleTimeSlotDialogClose,
    };
  },
};
</script>
<style lang="scss" scoped>
.deviceReservation {
  .pagination {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }
}

:deep(.el-dialog__body) {
  margin-top: 20px;
}

:deep(.el-dialog__footer) {
  text-align: center;
}

:deep(.el-table .el-button--text) {
  padding: 0;
  margin-right: 10px;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}
</style>
